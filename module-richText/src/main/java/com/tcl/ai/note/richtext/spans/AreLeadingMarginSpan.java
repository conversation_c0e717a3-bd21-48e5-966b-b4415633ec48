package com.tcl.ai.note.richtext.spans;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;
import android.text.style.LeadingMarginSpan;

import com.tcl.ai.note.richtext.converter.MarginStyleConverter;
import com.tcl.ai.note.richtext.inner.Constants;
import com.tcl.ai.note.richtext.utils.LogUtils;

public class AreLeadingMarginSpan implements LeadingMarginSpan, ARE_Span {

	// 使用两个汉字作为参考宽度

	private int mStandardLeading;
	private int mLevel = 0;

	private int mLeadingMargin = mStandardLeading;

	public AreLeadingMarginSpan() {
		mStandardLeading = MarginStyleConverter.INSTANCE.calculateChineseCharacterWidth();
		mLeadingMargin = mStandardLeading;
	}

	@Override
	public int getLeadingMargin(boolean first) {
		return mLeadingMargin;
	}

	@Override
	public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top,
                                  int baseline, int bottom, CharSequence text, int start, int end,
                                  boolean first, Layout layout) {
		LogUtils.d("AreLeadingMarginSpan", "drawLeadingMargin() ca  mLeadingMargin=" + mLeadingMargin);
		c.drawText(Constants.ZERO_WIDTH_SPACE_STR, x + dir + mLeadingMargin, baseline, p);
	}

	/**
	 * Set leading level
	 *
	 * @param level
	 */
	public void setLevel(int level) {
		mLevel = level;
		mLeadingMargin = mStandardLeading * mLevel;
		LogUtils.d("AreLeadingMarginSpan", "setLevel() called with: level = [" + level + "] mLeadingMargin=" + mLeadingMargin);
	}

	public int getLevel() {
		return mLevel;
	}

	/**
	 * Increase leading level.
	 */
	public void increaseLevel() {
			++mLevel;
			mLeadingMargin = mStandardLeading * mLevel;
		LogUtils.d("AreLeadingMarginSpan", "increaseLevel() called mLeadingMargin=" + mLeadingMargin);
	}

	/**
	 * Decrease leading level.
	 *
	 * @return
	 */
	public int decreaseLevel() {
		--mLevel;
		if (mLevel < 0) {
			mLevel = 0;
		}
		mLeadingMargin = mStandardLeading * mLevel;
		LogUtils.d("AreLeadingMarginSpan", "decreaseLevel() called mLeadingMargin=" + mLeadingMargin);
		return mLevel;
	}



	@Override
	public String getHtml() {
		if (mLevel <= 0) {
			return "";
		}
		// 使用CSS的margin-left来实现左缩进效果
		// 每个level对应2em的缩进（大约两个汉字的宽度）
		return String.format(" style=\"margin-left: %dem;\"", mLevel * 2);
	}
}
