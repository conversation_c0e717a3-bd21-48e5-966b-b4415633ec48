package com.tcl.ai.note.richtext.history

import android.text.Spannable
import android.text.SpannableStringBuilder
import com.tcl.ai.note.richtext.converter.MarginStyleConverter.calculateChineseCharacterWidth
import com.tcl.ai.note.richtext.spans.AreLeadingMarginSpan
import com.tcl.ai.note.richtext.spans.UpcomingListSpan
import com.tcl.ai.note.richtext.styles.ARE_Upcoming
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.richtext.views.AREditText
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import java.util.*
import kotlin.math.abs

/**
 * 富文本编辑历史记录管理器
 * 
 * 负责跟踪文本内容和样式变化，支持撤销和重做操作
 */
class RichTextUndoRedoManager(private val editText: AREditText) {
    
    companion object {
        private const val TAG = "TextEditHistory"
        private const val MAX_HISTORY_SIZE = 50 // 最大历史记录数量
        private const val MERGE_TIMEOUT = 1000L  // 合并连续操作的超时时间（毫秒）
    }

    // 撤销栈和重做栈
    private val undoStack = LinkedList<RichTextEditOperation>()
    private val redoStack = LinkedList<RichTextEditOperation>()
    
    // 上次操作时间，用于判断是否合并连续操作
    private var lastOperationTime = 0L
    
    // 是否正在执行撤销/重做操作（防止循环触发）
    private var isPerformingUndoRedo = false
    
    // 当前文本状态的快照
    private var currentSpanned: Spannable? = null

    // 保存初始状态
    private var initSpanned: Spannable? = null

    fun initState(text: Spannable) {
        initSpanned = SpannableStringBuilder(text)
    }

    /**
     * 记录编辑操作
     * @param start 操作开始位置
     * @param end 操作结束位置
     * @param operationType 操作类型
     * @param spanClass 样式类型（如果是样式操作）
     */
    fun recordOperation(
        start: Int,
        end: Int,
        operationType: OperationType,
        spanClass: Class<*>? = null,
    ) {
        recordOperation(start, end, operationType, spanClass, 0)
    }

    /**
     * 记录编辑操作
     * @param start 操作开始位置
     * @param end 操作结束位置
     * @param operationType 操作类型
     * @param spanClass 样式类型（如果是样式操作）
     * @param indentLevel 缩进级别（如果是缩进操作）
     * @param isChecked 待办事项选中状态（如果是待办事项操作）
     */
    fun recordOperation(
        start: Int,
        end: Int,
        operationType: OperationType,
        spanClass: Class<*>? = null,
        indentLevel: Int = 0,
        isChecked: Boolean = false
    ) {
        try {
            if (isPerformingUndoRedo) return

            // 获取当前文本状态
            val text = editText.text

            // 创建当前状态的快照
            val newSpanned = SpannableStringBuilder(text)
            var temp = initSpanned
            if (temp == null) {
                temp = newSpanned
            }

            // 创建操作记录
            val operation = RichTextEditOperation(
                beforeSpanned = currentSpanned ?: temp,
                afterSpanned = newSpanned,
                start = start,
                end = end,
                operationType = operationType,
                spanClass = spanClass,
                indentLevel = indentLevel,
                isChecked = isChecked,
                timestamp = System.currentTimeMillis()
            )
            // 如果是首次操作，直接保存当前状态
            if (currentSpanned == null) {
                currentSpanned = newSpanned
                // 添加新操作
                undoStack.add(operation)
                onUndoStackAdded(operation)
                // 更新撤销/重做按钮状态
                updateUndoRedoState()
                return
            }

            // 检查是否可以合并连续操作
            val shouldMerge = shouldMergeWithLastOperation(operation)

            Logger.e(TAG, "shouldMerge: ${shouldMerge}")
            if (shouldMerge && undoStack.isNotEmpty()) {
                // 合并操作
                val lastOperation = undoStack.removeLast()
                onUndoStackRemoved(lastOperation)
                val mergedOperation = RichTextEditOperation(
                    beforeSpanned = lastOperation.beforeSpanned,
                    afterSpanned = newSpanned,
                    start = lastOperation.start,
                    end = operation.end,
                    operationType = operation.operationType,
                    spanClass = operation.spanClass,
                    indentLevel = operation.indentLevel,
                    isChecked = operation.isChecked,
                    timestamp = operation.timestamp
                )
                undoStack.add(mergedOperation)
                onUndoStackAdded(mergedOperation)
            } else {
                // 添加新操作
                undoStack.add(operation)
                onUndoStackAdded(operation)
                // 限制历史记录大小
                if (undoStack.size > MAX_HISTORY_SIZE) {
                    undoStack.removeFirst()
                }
            }

            // 清空重做栈
            redoStack.clear()

            // 更新当前状态
            currentSpanned = newSpanned

            // 更新上次操作时间
            lastOperationTime = System.currentTimeMillis()

            // 更新撤销/重做按钮状态
            updateUndoRedoState()

            Logger.d(TAG, "recordOperation: ${operation.operationType}, position: ${operation.start}-${operation.end}")
        } catch (e: Exception) {
            Logger.e(TAG, "EditText add undo operation failed: ${e.message}")
        }
    }
    
    /**
     * 执行撤销操作
     * @return 是否成功撤销
     */
    fun undo(): Boolean {
        if (undoStack.isEmpty()) return false
        
        isPerformingUndoRedo = true
        
        try {
            val operation = undoStack.removeLast()
            redoStack.add(operation)
            
            // 恢复到操作前的状态，并传递操作信息和撤销标志
            restoreSpanned(operation.beforeSpanned, operation, true)
            
            // 更新当前状态
            currentSpanned = operation.beforeSpanned
            
            Logger.d(TAG, "canUndo: ${undoStack.size}")
            
            // 更新撤销/重做按钮状态
            updateUndoRedoState()
            RichTextEventManager.clearStyleEvent()
            editText.saveContent(editText.text.toString())
            return true
        } catch (e: Exception) {
            Logger.e(TAG, "EditText undo failed: ${e.message}")
            return false
        } finally {
            isPerformingUndoRedo = false
        }
    }
    
    /**
     * 执行重做操作
     * @return 是否成功重做
     */
    fun redo(): Boolean {
        if (redoStack.isEmpty()) return false
        
        isPerformingUndoRedo = true
        
        try {
            val operation = redoStack.removeLast()
            undoStack.add(operation)
            
            // 恢复到操作后的状态，并传递操作信息和重做标志
            restoreSpanned(operation.afterSpanned, operation, false)
            
            // 更新当前状态
            currentSpanned = operation.afterSpanned
            
            Logger.d(TAG, "redo: ${operation.operationType}, position: ${operation.start}-${operation.end}")
            
            // 更新撤销/重做按钮状态
            updateUndoRedoState()
            editText.saveContent(editText.text.toString())

            return true
        } catch (e: Exception) {
            Logger.e(TAG, "EditText redo failed: ${e.message}")
            return false
        } finally {
            isPerformingUndoRedo = false
        }
    }
    
    /**
     * 清空历史记录
     */
    fun clearHistory() {
        undoStack.clear()
        redoStack.clear()
        currentSpanned = editText.text?.let { SpannableStringBuilder(it) }
        updateUndoRedoState()
    }
    
    /**
     * 判断是否可以撤销
     */
    fun canUndo(): Boolean {
        return undoStack.isNotEmpty()
    }
    
    /**
     * 判断是否可以重做
     */
    fun canRedo(): Boolean {
        return redoStack.isNotEmpty()
    }
    
    /**
     * 恢复Spanned对象
     * @param spanned 要恢复的Spannable对象
     * @param operation 操作信息
     * @param isUndo 是否是撤销操作（true为撤销，false为重做）
     */
    private fun restoreSpanned(spanned: Spannable, operation: RichTextEditOperation? = null, isUndo: Boolean = true) {
        // 停止监听文本变化
        editText.stopAllMonitor()
        
        try {
            // 直接设置文本内容，包括所有样式
            editText.setText(spanned)
            
            // 如果是待办事项操作，需要特殊处理选中状态
            if (operation?.spanClass == UpcomingListSpan::class.java) {
                handleUpcomingListSpanRestore(spanned, operation, isUndo)
            }

            // 如果是缩进操作，需要特殊处理缩进级别
            if (operation?.operationType == OperationType.INDENT_RIGHT ||
                operation?.operationType == OperationType.INDENT_LEFT) {
                handleIndentRestore(spanned, operation, isUndo)
            }

            restoreCursorPosition(operation, isUndo, spanned)
            editText.startAllMonitor()

            val editable = editText.text
            val numberSpans = editable?.getSpans(0, editable.length, com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan::class.java)
            if (numberSpans != null && numberSpans.isNotEmpty()) {
                for (style in editText.stylesList) {
                    if (style is com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListNumber) {
                        style.reNumberAllListItemsPublic(editable)
                        break
                    }
                }
            }
            // 触发样式状态更新
            editText.onSelectionChanged(editText.selectionStart, editText.selectionEnd)
        } catch (e: Exception) {
            Logger.e(TAG, "EditText restore spanned failed: ${e.message}")
        } finally {
            // 恢复监听
            editText.startAllMonitor()
        }
    }

    /**
     * 恢复光标位置
     */
    private fun restoreCursorPosition(
        operation: RichTextEditOperation?,
        isUndo: Boolean,
        spanned: Spannable
    ) {
        if (operation != null) {
            // 根据操作类型和是否为撤销操作决定光标位置
            when (operation.operationType) {
                OperationType.TEXT_INSERT -> {
                    if (isUndo) {
                        // 撤销插入操作时，光标应该在插入位置
                        val cursorPos = operation.start
                        val safePos = cursorPos.coerceIn(0, spanned.length)
                        editText.setSelection(safePos)
                    } else {
                        // 重做插入操作时，光标应该在插入内容的末尾
                        val cursorPos = operation.end
                        val safePos = cursorPos.coerceIn(0, spanned.length)
                        editText.setSelection(safePos)
                    }
                }

                OperationType.TEXT_DELETE -> {
                    if (isUndo) {
                        // 撤销删除操作时，光标应该在恢复的文字后面
                        val cursorPos = operation.end
                        val safePos = cursorPos.coerceIn(0, spanned.length)
                        editText.setSelection(safePos)
                    } else {
                        // 重做删除操作时，光标应该在删除位置
                        val safePos = operation.start.coerceIn(0, spanned.length)
                        editText.setSelection(safePos)
                    }
                }

                OperationType.STYLE_APPLY, OperationType.STYLE_REMOVE -> {
                    // 应用或移除样式后，保持选区不变
                    val safeStart = operation.start.coerceIn(0, spanned.length)
                    val safeEnd = operation.end.coerceIn(safeStart, spanned.length)
                    val hasSelection = operation.start != operation.end
                    if (hasSelection) {
                        editText.setSelection(safeStart, safeEnd)
                    } else {
                        editText.setSelection(safeEnd)
                    }
                }

                OperationType.PARAGRAPH_CHANGE -> {
                    // 段落变化后，光标应该在段落末尾
                    val safeEnd = operation.end.coerceIn(0, spanned.length)
                    editText.setSelection(safeEnd)
                }

                OperationType.INDENT_CHANGE -> {
                    // 缩进变化后，保持选区不变
                    val safeStart = operation.start.coerceIn(0, spanned.length)
                    val safeEnd = operation.end.coerceIn(safeStart, spanned.length)
                    editText.setSelection(safeStart, safeEnd)

                    // 如果需要，可以在这里添加额外的缩进恢复逻辑
                    Logger.d(
                        TAG,
                        "restore indent, start: ${operation.start}, end: ${operation.end}, isUndo: $isUndo"
                    )
                }

                OperationType.INDENT_RIGHT -> {}
                OperationType.INDENT_LEFT -> {}
            }
        } else {
            // 如果没有操作信息，将光标放在文本末尾
            editText.setSelection(spanned.length)
        }
    }


    /**
     * 处理待办事项的撤销/重做
     */
    private fun handleUpcomingListSpanRestore(spanned: Spannable, operation: RichTextEditOperation, isUndo: Boolean) {
        spanned.let { text ->
            val spans = text.getSpans(0, text.length, UpcomingListSpan::class.java)

            // 对于待办事项操作，根据操作记录的isChecked状态来设置
            val targetChecked = if (isUndo) !operation.isChecked else operation.isChecked

            // 找到操作范围内的待办事项
            for (span in spans) {
                val start = text.getSpanStart(span)
                val end = text.getSpanEnd(span)

                // 检查是否在操作范围内
                if (start <= operation.end && end >= operation.start) {
                    span.isChecked = targetChecked

                    // 更新删除线
                    val areUpcoming = ARE_Upcoming(editText.context)
                    areUpcoming.setEditText(editText)
                    areUpcoming.toggleStrikeboundSpan(targetChecked, span)

                    Logger.d(TAG, "restore todo span checked: $targetChecked, position: $start-$end")
                }
            }
        }
    }

    /**
     * 处理缩进的撤销/重做
     */
    private fun handleIndentRestore(spanned: Spannable, operation: RichTextEditOperation, isUndo: Boolean) {
        spanned.let { text ->
            val spans = text.getSpans(0, text.length, AreLeadingMarginSpan::class.java)

            // 找到操作范围内的缩进
            for (span in spans) {
                val start = text.getSpanStart(span)
                val end = text.getSpanEnd(span)

                // 检查是否在操作范围内
                if (start <= operation.end && end >= operation.start) {
                    // 根据操作类型和是否为撤销操作决定缩进级别
                    val targetLevel = if (isUndo) {
                        // 如果是撤销操作，需要恢复到操作前的级别
                        if (operation.operationType == OperationType.INDENT_RIGHT) {
                            // 撤销右缩进，应该减少一级
                            Math.max(0, operation.indentLevel - 1)
                        } else {
                            // 撤销左缩进，应该增加一级
                            operation.indentLevel + 1
                        }
                    } else {
                        // 如果是重做操作，直接使用记录的级别
                        operation.indentLevel
                    }

                    // 设置缩进级别
                    span.setLevel(targetLevel)

                    // 确保mLeadingMargin也被正确设置
                    val standardLeading = calculateChineseCharacterWidth()
                    val leadingMargin = standardLeading * targetLevel

                    // 使用反射设置mLeadingMargin字段
                    try {
                        val field = AreLeadingMarginSpan::class.java.getDeclaredField("mLeadingMargin")
                        field.isAccessible = true
                        field.setInt(span, leadingMargin)
                    } catch (e: Exception) {
                        Logger.e(TAG, "set mLeadingMargin error: ${e.message}")
                    }

                    Logger.d(TAG, " restore indent level: $targetLevel, position: $start-$end")
                }
            }
        }
    }

    /**
     * 判断是否应该合并连续操作
     */
    private fun shouldMergeWithLastOperation(operation: RichTextEditOperation): Boolean {
        if (undoStack.isEmpty()) return false
        
        val lastOperation = undoStack.last
        
        // 检查时间间隔
        val timeDiff = operation.timestamp - lastOperation.timestamp
        if (timeDiff > MERGE_TIMEOUT) return false

        Logger.e(TAG, "shouldMerge: operationType: ${operation.operationType}, spanClass: ${operation.spanClass}, ")
        // 检查操作类型
//        if (operation.operationType != lastOperation.operationType) return false
        
        // 检查样式类型
//        if (operation.spanClass != lastOperation.spanClass) return false
        
        // 检查缩进级别
        if (operation.operationType == OperationType.INDENT_CHANGE &&
            operation.indentLevel != lastOperation.indentLevel) return false

        // 检查待办事项选中状态
        if (operation.spanClass == UpcomingListSpan::class.java &&
            operation.isChecked != lastOperation.isChecked) return false
        Logger.e(TAG, "shouldMerge: operation.start: ${operation.start}, lastOperation.start: ${lastOperation.start},lastOperation.end：${lastOperation.end + 1} ")
        // 检查位置连续性
        return abs(operation.start - lastOperation.start)<=1 && abs(operation.start - lastOperation.end)<=2
    }
    
    /**
     * 更新撤销/重做按钮状态
     */
    private fun updateUndoRedoState() {
        // 通知外部状态变化
        editText.updateUndoRedoState(canUndo(), canRedo())
    }

    /**
     * 有新的操作，添加到undo栈中
     * 注：不包含redo操作时，添加的undo操作
     */
    private fun onUndoStackAdded(operation: RichTextEditOperation) {
        // 通知外部状态变化
        editText.onUndoStackAdded(operation)
    }

    /**
     * undo栈被删除元素
     * 注：非实际的undo操作
     */
    private fun onUndoStackRemoved(operation: RichTextEditOperation) {
        // 通知外部状态变化
        editText.onUndoStackRemoved(operation)
    }

}
