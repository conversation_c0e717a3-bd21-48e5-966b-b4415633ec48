package com.tcl.ai.note.richtext.styles;

import android.text.Editable;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.richtext.views.AREditText;

/**
 * 样式接口
 */
public interface IARE_Style {
    /**
     * 获取图标视图
     */
    ImageView getImageView();

    /**
     * 设置选中状态
     */
    void setChecked(boolean isChecked);

    /**
     * 获取选中状态
     */
    boolean getIsChecked();

    /**
     * 设置有效状态
     */
    void setisValid(boolean isValid);

    /**
     * 获取有效状态
     */
    boolean getIsValid();

    /**
     * 设置编辑框
     */
    void setEditText(AREditText editText);

    /**
     * 获取编辑框
     */
    EditText getEditText();

    /**
     * 更新选中状态
     */
    void updateCheckStatus(boolean checked);

  void setListenerForImageView(ImageView imageView);

  void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory);

  void removeStyle(Editable editable, int start, int end);

  /**
     * 设置样式状态监听器
     */
    void setStyleStatusListener(StyleStatusListener listener);

    /**
     * 是否需要重新刷新样式
     * @return
     */
    Boolean needApplyStyle();
}
