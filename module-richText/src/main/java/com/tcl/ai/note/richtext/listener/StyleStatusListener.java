package com.tcl.ai.note.richtext.listener;

import android.text.Layout;

/**
 * 富文本样式操作监听器
 * 用于处理各种富文本样式的应用操作
 */
public interface StyleStatusListener {
    /**
     * 切换待办事项
     * @param isActive 当前是否已激活
     */
    void onTodoToggled(boolean isActive);
    /**
     * 切换粗体样式
     * @param isActive 当前是否已激活
     */
    void onBoldToggled(boolean isActive);
    
    /**
     * 切换斜体样式
     * @param isActive 当前是否已激活
     */
    void onItalicToggled(boolean isActive);
    
    /**
     * 切换下划线样式
     * @param isActive 当前是否已激活
     */
    void onUnderlineToggled(boolean isActive);
    
    /**
     * 切换删除线样式
     * @param isActive 当前是否已激活
     */
    void onStrikethroughToggled(boolean isActive);
    
    /**
     * 应用文本对齐方式
     * @param alignment 对齐方式
     */
    void onAlignmentApplied(Layout.Alignment alignment);
    
    /**
     * 应用左缩进
     */
    void onIndentLeftApplied(boolean isActive);
    
    /**
     * 应用右缩进
     */
    void onIndentRightApplied(boolean isActive);
    
    /**
     * 切换有序列表
     * @param isActive 当前是否已激活
     */
    void onNumberedListToggled(boolean isActive);
    
    /**
     * 切换无序列表
     * @param isActive 当前是否已激活
     */
    void onBulletedListToggled(boolean isActive);
    
    /**
     * 应用字体大小
     * @param size 字体大小
     */
    void onFontSizeApplied(int size);
    
    /**
     * 应用字体颜色
     * @param color 颜色值
     */
    void onFontColorApplied(int color);
    
    /**
     * 应用背景颜色
     * @param color 颜色值
     */
    void onBackgroundColorApplied(int color);
}