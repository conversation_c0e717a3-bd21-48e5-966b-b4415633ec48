package com.tcl.ai.note.richtext.spans;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.Layout;
import android.text.Spanned;
import android.text.TextPaint;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.tcl.ai.note.GlobalContext;
import com.tcl.ai.note.richtext.R;
import com.tcl.ai.note.richtext.Commands.PM_Commands;
import com.tcl.ai.note.richtext.converter.RichTextStyleEntityToSpanConverter;
import com.tcl.ai.note.richtext.utils.DisplayUtils;
import com.tcl.ai.note.utils.Logger;


/**
 * author: xuyuan
 * created on:19-7-29 am 11:13
 * description:Note upcoming list span style
 */
public class UpcomingListSpan implements AbstractClickSpan,AreListSpan {
    private static final String TAG = UpcomingListSpan.class.getSimpleName();
    private int mImageWidth;
    private TextPaint mBluePaint;
    private TextPaint mRedPaint;
    private Drawable mCheckedDrawable = null;
    private Drawable mUncheckedDrawable = null;
    private int LINE_PADDING_TOP;
    private int LINE_PADDING_BOTTOM;
    private boolean mIsChecked;

    private float mLeadingTopY;

    private float mLeadingBottomY;

    private float mClickAreaPadding;

    private int direction = 1;

    private float  paragraph_leading_ratio;

    public UpcomingListSpan(int imageWidth) {
        this.mImageWidth = imageWidth;
        init();
    }

    public UpcomingListSpan() {
        this(RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Edit());
    }

    @SuppressLint("ResourceType")
    private void init() {
        LINE_PADDING_TOP = GlobalContext.Companion.getInstance().getResources()
                .getDimensionPixelSize(R.dimen.edit_note_content_line_padding_top);
        LINE_PADDING_BOTTOM = GlobalContext.Companion.getInstance().getResources()
                .getDimensionPixelSize(R.dimen.edit_note_content_line_padding_bottom);
        mBluePaint = new TextPaint();
        mBluePaint.setColor(Color.BLUE);

        mRedPaint = new TextPaint();
        mRedPaint.setColor(Color.RED);

        mClickAreaPadding = GlobalContext.Companion.getInstance().getResources().getDimensionPixelSize(R.dimen.edit_note_upcoming_click_area_padding);
        TypedValue typedValue = new TypedValue();
        GlobalContext.Companion.getInstance().getResources().getValue(R.integer.paragraph_leading_ratio,
                typedValue, false);
        paragraph_leading_ratio = typedValue.getFloat();

    }

    public void setChecked(boolean isChecked) {
        mIsChecked = isChecked;
    }

    public boolean isChecked(){
        return mIsChecked;
    }


    @Override
    public int getLeadingMargin(boolean first) {
        boolean isHome = mImageWidth == RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Home();
        int leadingMargin = DisplayUtils.dp2px(isHome ? 20 : 28);
//        Logger.d(TAG, "getLeadingMargin: mImageWidth=" + mImageWidth +
//                " todoImageWidth_Home=" + RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Home() +
//                " isHome=" + isHome + " leadingMargin=" + leadingMargin);
        return leadingMargin;
    }

    @Override
    public void drawLeadingMargin(Canvas canvas, Paint p, int x, int dir,
                                  int top, int baseline, int bottom, CharSequence text,
                                  int start, int end, boolean first, Layout layout) {
        Logger.d(TAG, "drawLeadingMargin() called with: canvas = [" + canvas + "], p = [" + p + "], x = [" + x + "], dir = [" + dir + "], top = [" + top + "], baseline = [" + baseline + "], bottom = [" + bottom + "], text = [" + text + "], start = [" + start + "], end = [" + end + "], first = [" + first + "], layout = [" + layout + "]");
        if (((Spanned) text).getSpanStart(this) == start) {
            Paint.Style style = p.getStyle();
            p.setStyle(Paint.Style.FILL);
//
//            canvas.drawText("\u2022", x + dir + LEADING_MARGIN, baseline, p);

            direction = dir;
            Drawable drawable = getDrawable();
            Paint.FontMetricsInt fontMetrics = p.getFontMetricsInt();

            float decentY = baseline + fontMetrics.descent;
            float ascentY = top + (fontMetrics.ascent - fontMetrics.top);

            float transY = top;
            float transX = x;
            transY += ((decentY - ascentY) - drawable.getBounds().height()) / 2.0f + DisplayUtils.dp2px(2);
            if(dir <0 )
                transX = x - drawable.getBounds().width();

            Logger.d(TAG, "drawLeadingMargin: transX=" +transX+  " transY=" + transY);
            canvas.save();
            float leadingMargin = 0f;
//            if (!BuildConfigUtils.isTablet()) {
//                leadingMargin = Constants.PARAGRAPH_LEADING_MARGIN;
//            }
            canvas.translate(transX-leadingMargin, transY);
            drawable.draw(canvas);
            canvas.restore();
            mLeadingTopY = ascentY;
            mLeadingBottomY = mLeadingTopY + drawable.getBounds().height();
            p.setStyle(style);
        }
    }

//    @Override
//    public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
//        Drawable d = getDrawable();
//        Rect rect = d.getBounds();
//        Paint.FontMetricsInt fmi = new Paint.FontMetricsInt();
//        if (fm != null) {
//            fm.ascent = fmi.ascent;
//            fm.descent = fmi.descent;
//
//            fm.top = fmi.top;
//            fm.bottom = fmi.bottom;
//        }
//
//        return rect.right;
//    }

//
//
//    @Override
//    public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int baseLine, int bottom, Paint paint) {
//        Drawable drawable = getDrawable();
//        int visualTop = top - LINE_PADDING_TOP;
//        int visualBottom = baseLine + LINE_PADDING_BOTTOM;
//        int transY = visualTop + ((visualBottom - visualTop) - drawable.getBounds().height()) / 2;
//        canvas.translate(x, transY);
//        drawable.draw(canvas);
//        canvas.translate(-x, -transY);
//    }

    public Drawable getDrawable() {
//        boolean checked = mUpcomingItem.isChecked();
        if (isChecked()) {
            if (mCheckedDrawable == null) {
                mCheckedDrawable = decodeDrawable(GlobalContext.Companion.getInstance(), R.drawable.ic_todo_checked);
            }
            return mCheckedDrawable;
        } else {
            if (mUncheckedDrawable == null) {
                mUncheckedDrawable = decodeDrawable(GlobalContext.Companion.getInstance(), R.drawable.ic_todo_unchecked);
            }
            return mUncheckedDrawable;
        }
    }

    public  Drawable decodeDrawable(Context context, int resId) {
        try {
            int px = DisplayUtils.dp2px(mImageWidth);
            Drawable drawable = ContextCompat.getDrawable(context, resId);
            drawable.setBounds(0, 0,px ,
                    px);
            return drawable;
        } catch (Exception e) {
            Logger.d(TAG, "Unable to find resource: " + resId);
        }
        return null;
    }

    @Override
    public void onClick(View view) {
        Logger.d("onClick this=" + this + " view=" + view);
        setChecked(!isChecked());

        PM_Commands.getInstance().notifyText(PM_Commands.EVENT_TODO_SPAN_CLICK,UpcomingListSpan.this,null);

        Logger.d("Upcoming click over ...." + isChecked());
    }

    @Override
    public boolean isClickValid(TextView widget, MotionEvent event, int lineBottom) {
        int paddingLeft = widget.getTotalPaddingLeft();
        int paddingTop = widget.getTotalPaddingTop();
        int minX, maxX;
        if(direction > 0) {
            minX = 0;
            maxX = getLeadingMargin(true);
        }else {
            maxX = widget.getWidth();
            minX = maxX - getLeadingMargin(true);
        }

//        Log.d(TAG, "isClickValid: maxX=" + maxX + " minX=" + minX);

        int clickX = (int) event.getX();
        int clickY = (int) event.getY();
//        Log.d(TAG, "isClickValid: clickX=" + clickX + " clickY=" + clickY);
//        if(widget.isLayoutRtl()){
//            int width = mContext.getResources().getDisplayMetrics().widthPixels;
//            minX = width - paddingLeft -mImageWidth;
//            maxX = width - paddingLeft;
//        }
//            int width = mContext.getResources().getDisplayMetrics().widthPixels;
//            minX = width - paddingLeft -mImageWidth;
//            maxX = width - paddingLeft;
        return (clickX > paddingLeft - minX - mClickAreaPadding && clickX < paddingLeft + maxX + mClickAreaPadding)
                && (clickY >paddingTop - (mLeadingTopY - mClickAreaPadding) && clickY < paddingTop + mLeadingBottomY + mClickAreaPadding);
    }
}
