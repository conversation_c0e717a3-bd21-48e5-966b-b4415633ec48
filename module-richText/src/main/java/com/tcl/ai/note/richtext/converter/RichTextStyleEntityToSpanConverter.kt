package com.tcl.ai.note.richtext.converter

import android.text.Editable
import android.text.Layout
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.*
import android.widget.EditText
import android.widget.TextView
import com.tcl.ai.note.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.richtext.data.StyleRange
import com.tcl.ai.note.richtext.spans.*
import androidx.core.graphics.toColorInt
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.runIO
import kotlinx.coroutines.coroutineScope
import kotlin.math.max
import kotlin.math.min

/**
 * RichTextStyleEntity 与 EditText Span 的转换器
 * SPAN_INCLUSIVE_EXCLUSIVE: 适用于需要扩展起始位置但不扩展结束位置的情况，如段落样式 原htmltoSpan 里 字体样式是这个
 * SPAN_INCLUSIVE_INCLUSIVE: 适用于需要在两端都扩展的情况，如文本高亮
 * SPAN_EXCLUSIVE_EXCLUSIVE: 适用于需要严格限制范围的情况，如链接
 * SPAN_EXCLUSIVE_INCLUSIVE: 适用于需要在结束位置扩展但不在起始位置扩展的情况，如列表项,左右对齐, 待办事项
 */
object RichTextStyleEntityToSpanConverter {

    const val TODO_FLAG = "[ ]"
    const val TODO_FLAG_CHECK = "[v]"
    const val BULLET_FLAG = "•"
    // 待办事项图标大小 首页会修改
    var todoImageWidth_Edit=24 // dp
    var todoImageWidth_Home=18 // dp

    /**
     * 将 RichTextStyleEntity 应用到 TextView（用于显示）
     */
    fun applyToTextView(noteData: RichTextStyleEntity, textView: TextView, text: String,todoImageWidth:Int) {
        val spannableStringBuilder = SpannableStringBuilder(text)

        // ===== 1. 对list做区间去重（同区间只保留一个，防叠加）=====
        val cleanedList = dedupListRanges(noteData.list)

        // 应用颜色样式
        noteData.fontColor.forEach { range ->
            try {
                range.value.let { value ->
                    val color = value.toColorInt()
                    spannableStringBuilder.setSpan(
                        AreForegroundColorSpan(color),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用字体大小样式
        noteData.fontSize.forEach { range ->
            try {
                range.value.let { value ->
                    val size = parseFontSize(value)
                    spannableStringBuilder.setSpan(
                        AreFontSizeSpan(size),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用对齐样式
        noteData.align.forEach { range ->
            try {
                range.value.let { value ->
                    val alignment = parseAlignment(value)
                    spannableStringBuilder.setSpan(
                        AlignmentSpan.Standard(alignment),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用粗体样式
        noteData.bold.forEach { range ->
            spannableStringBuilder.setSpan(
                AreBoldSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }

        // 应用斜体样式
        noteData.italic.forEach { range ->
            spannableStringBuilder.setSpan(
                AreItalicSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }

        // 应用下划线样式
        noteData.underline.forEach { range ->
            spannableStringBuilder.setSpan(
                AreUnderlineSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }

        // 应用删除线样式
        noteData.strikethrough.forEach { range ->
            spannableStringBuilder.setSpan(
                StrikethroughSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }

        // 应用背景色样式
        noteData.backgroundColor.forEach { range ->
            try {
                range.value.let { value ->
                    val color = value.toColorInt()
                    spannableStringBuilder.setSpan(
                        AreBackgroundColorSpan(color),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用缩进样式
        noteData.indent.forEach { range ->
            val level = range.value.toIntOrNull()?: 0
            val areLeadingMarginSpan = AreLeadingMarginSpan()
            areLeadingMarginSpan.level=level
            spannableStringBuilder.setSpan(
                areLeadingMarginSpan,
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        // 应用列表项样式
        cleanedList.forEach { range ->
            applyListSpan(spannableStringBuilder, range,todoImageWidth)
        }

        textView.text = spannableStringBuilder
    }

    /**
     * 将 RichTextStyleEntity 应用到 EditText
     */
    fun applyToEditText(noteData: RichTextStyleEntity, editText: EditText, text: String) {
        val spannableStringBuilder = SpannableStringBuilder(text)

        // ===== 1. 对list做区间去重（同区间只保留一个，防叠加）=====
        val cleanedList = dedupListRanges(noteData.list)
        
        // 应用颜色样式
        noteData.fontColor.forEach { range ->
            try {
                range.value.let { value ->
                    val color = value.toColorInt()
                    spannableStringBuilder.setSpan(
                        AreForegroundColorSpan(color),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        
        // 应用字体大小样式
        noteData.fontSize.forEach { range ->
            try {
                range.value.let { value ->
                    val size = parseFontSize(value)
                    spannableStringBuilder.setSpan(
                        AreFontSizeSpan(size),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        
        // 应用对齐样式
        noteData.align.forEach { range ->
            try {
                range.value.let { value ->
                    val alignment = parseAlignment(value)
                    spannableStringBuilder.setSpan(
                        AlignmentSpan.Standard(alignment),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        
        // 应用粗体样式
        noteData.bold.forEach { range ->
            spannableStringBuilder.setSpan(
                AreBoldSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }
        
        // 应用斜体样式
        noteData.italic.forEach { range ->
            spannableStringBuilder.setSpan(
                AreItalicSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }
        
        // 应用下划线样式
        noteData.underline.forEach { range ->
            spannableStringBuilder.setSpan(
                AreUnderlineSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }
        // 应用删除线样式
        noteData.strikethrough.forEach { range ->
            spannableStringBuilder.setSpan(
                StrikethroughSpan(),
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE
            )
        }
        noteData.backgroundColor.forEach { range ->
            try {
                range.value.let { value ->
                    val color = value.toColorInt()
                    spannableStringBuilder.setSpan(
                        AreBackgroundColorSpan(color),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        noteData.indent.forEach { range ->
            val level = range.value.toIntOrNull()?: 0
            val areLeadingMarginSpan = AreLeadingMarginSpan()
            areLeadingMarginSpan.level=level
            spannableStringBuilder.setSpan(
                areLeadingMarginSpan,
                range.start,
                range.end,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        
        // 应用列表项样式
//        noteData.list.forEach { range ->
//            applyListSpan(spannableStringBuilder, range)
//        }

        cleanedList.forEach { range ->
            applyListSpan(spannableStringBuilder, range,todoImageWidth_Edit)
        }

        editText.text = spannableStringBuilder
    }

    /**
     * 将 RichTextStyleEntity 应用到 EditText
     */
    suspend fun applyToCopyRichText(noteData: RichTextStyleEntity, start: Int, end: Int, text: CharSequence): SpannableStringBuilder = coroutineScope {
        runIO {
        val spannableStringBuilder = SpannableStringBuilder(text)
        val textLength = text.length
        // ===== 1. 对list做区间去重（同区间只保留一个，防叠加）=====
        val cleanedList = dedupListRanges(noteData.list)

        // 应用颜色样式
        noteData.fontColor.forEach { range ->
            try {
                if (inRange(start, end, range)) {
                    range.value.let { value ->
                        val color = value.toColorInt()
                        spannableStringBuilder.setSpan(
                            AreForegroundColorSpan(color),
                            max(max(start, range.start) - start, 0),
                            min(range.end - start, textLength),
                            Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                        )
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用字体大小样式
        noteData.fontSize.forEach { range ->
            try {
                if (inRange(start, end, range)) {
                    range.value.let { value ->
                        val size = parseFontSize(value)
                        spannableStringBuilder.setSpan(
                            AreFontSizeSpan(size),
                            max(max(start, range.start) - start, 0),
                            min(range.end - start, textLength),
                            Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                        )
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用对齐样式
        noteData.align.forEach { range ->
            try {
                if (inRange(start, end, range)) {
                    range.value.let { value ->
                        val alignment = parseAlignment(value)
                        spannableStringBuilder.setSpan(
                            AlignmentSpan.Standard(alignment),
                            max(max(start, range.start) - start, 0),
                            min(range.end - start, textLength),
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 应用粗体样式
        noteData.bold.forEach { range ->
            if (inRange(start, end, range)) {
                spannableStringBuilder.setSpan(
                    AreBoldSpan(),
                    max(max(start, range.start) - start, 0),
                    min(range.end - start, textLength),
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
        }

        // 应用斜体样式
        noteData.italic.forEach { range ->
            if (inRange(start, end, range)) {
                spannableStringBuilder.setSpan(
                    AreItalicSpan(),
                    max(max(start, range.start) - start, 0),
                    min(range.end - start, textLength),
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
        }

        // 应用下划线样式
        noteData.underline.forEach { range ->
            if (inRange(start, end, range)) {
                spannableStringBuilder.setSpan(
                    AreUnderlineSpan(),
                    max(max(start, range.start) - start, 0),
                    min(range.end - start, textLength),
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
        }
        // 应用删除线样式
        noteData.strikethrough.forEach { range ->
            if (inRange(start, end, range)) {
                spannableStringBuilder.setSpan(
                    StrikethroughSpan(),
                    max(max(start, range.start) - start, 0),
                    min(range.end - start, textLength),
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
        }
        noteData.backgroundColor.forEach { range ->
            try {
                if (inRange(start, end, range)) {
                    range.value.let { value ->
                        val color = value.toColorInt()
                        spannableStringBuilder.setSpan(
                            AreBackgroundColorSpan(color),
                            max(max(start, range.start) - start, 0),
                            min(range.end - start, textLength),
                            Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                        )
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        noteData.indent.forEach { range ->
            if (inRange(start, end, range)) {
                val level = range.value.toIntOrNull() ?: 0
                val areLeadingMarginSpan = AreLeadingMarginSpan()
                areLeadingMarginSpan.level = level
                spannableStringBuilder.setSpan(
                    areLeadingMarginSpan,
                    max(max(start, range.start) - start, 0),
                    min(range.end - start, textLength),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
        // 自定义待办、段落、数字格式
        val hash = HashMap<Int, String>()
        var offset = 0
        cleanedList.forEach { range ->
            if (inRange(start, end, range)) {
                // 选择的文本不在待办、有序、无序列表的起始位置，跳过
                if (start > range.start)    return@forEach
                val value = range.value
                when (value) {
                    "todo" -> {
                        hash.put(max(max(start, range.start) - start, 0), if (range.checked) TODO_FLAG_CHECK else TODO_FLAG)
                    }

                    "number" -> {
                        val number = range.number
                        hash.put(max(max(start, range.start) - start, 0), "$number.")
                    }

                    "bullet" -> {
                        hash.put(max(max(start, range.start) - start, 0), BULLET_FLAG)
                    }
                }
            }
        }

        hash.toSortedMap().forEach {
            spannableStringBuilder.insert(it.key + offset, it.value)
            offset += it.value.length
        }

        spannableStringBuilder
        }
    }

    // 判断是否在range区间内
    private fun inRange(start: Int, end: Int, range: StyleRange): Boolean {
        return start <= range.end && range.start <= end
    }

    fun applyPasteToRichText(styleRanges: List<StyleRange>, editable: Editable, layout: Layout) {
        styleRanges.forEach { range ->
            applyListSpan(editable, range)
        }
    }

    /**
     * list区间去重：仅保留同区间的最后一个，用于保证同一段只出现一个列表符
     */
    private fun dedupListRanges(list: List<StyleRange>): List<StyleRange> {
        // 可根据需要决定是否用last、first或自定义优先策略
        return list.groupBy { it.start to it.end }
            .map { it.value.last() }
            .sortedWith(compareBy({ it.start }, { it.end }))
    }

    /**
     * 应用列表项样式
     */
    private fun applyListSpan(spannable: SpannableStringBuilder, range: StyleRange,imageWidth:Int=todoImageWidth_Edit) {
        val value = range.value
        when (value) {
            "todo" -> {
                val checked= range.checked
                val upcomingListSpan = UpcomingListSpan(imageWidth)
                upcomingListSpan.isChecked = checked
                spannable.setSpan(
                    upcomingListSpan,
                    range.start,
                    range.end,
                    Spanned.SPAN_INCLUSIVE_INCLUSIVE
                )
                if (checked) {
                    spannable.setSpan(
                        ForegroundColorSpan(GlobalContext.appContext.getColor(com.tcl.ai.note.base.R.color.rt_todo_block_done_color)),
                        range.start,
                        range.end,
                        Spanned.SPAN_INCLUSIVE_INCLUSIVE
                    )
                }
            }
            "number" -> {
                val number = range.number
                spannable.setSpan(
                    ListNumberSpan(number),
                    range.start,
                    range.end,
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
            "bullet" -> {
                spannable.setSpan(
                    ListBulletSpan(),
                    range.start,
                    range.end,
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
        }
    }

    /**
     * 应用列表项样式
     */
    private fun applyListSpan(spannable: Editable, range: StyleRange) {
        val value = range.value
        // 先移除之前的样式，防止每次粘贴缩减和样式叠加
        spannable.getSpans(range.start, range.end, UpcomingListSpan::class.java).forEach {
            spannable.removeSpan(it)
        }
        spannable.getSpans(range.start, range.end, ForegroundColorSpan::class.java).forEach {
            spannable.removeSpan(it)
        }
        spannable.getSpans(range.start, range.end, StrikethroughSpan::class.java).forEach {
            spannable.removeSpan(it)
        }
        spannable.getSpans(range.start, range.end, ListNumberSpan::class.java).forEach {
            spannable.removeSpan(it)
        }
        spannable.getSpans(range.start, range.end, ListBulletSpan::class.java).forEach {
            spannable.removeSpan(it)
        }

        when (value) {
            "todo" -> {
                val checked= range.checked
                val upcomingListSpan = UpcomingListSpan(todoImageWidth_Edit)
                upcomingListSpan.isChecked = checked
                spannable.setSpan(
                    upcomingListSpan,
                    range.start,
                    range.end,
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
                if (checked) {
                    spannable.setSpan(
                        ForegroundColorSpan(GlobalContext.appContext.getColor(com.tcl.ai.note.base.R.color.rt_todo_block_done_color)),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                    spannable.setSpan(
                        StrikethroughSpan(),
                        range.start,
                        range.end,
                        Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                }
            }
            "number" -> {
                val number = range.number
                spannable.setSpan(
                    ListNumberSpan(number),
                    range.start,
                    range.end,
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
            "bullet" -> {
                spannable.setSpan(
                    ListBulletSpan(),
                    range.start,
                    range.end,
                    Spanned.SPAN_EXCLUSIVE_INCLUSIVE
                )
            }
        }
    }

    /**
     * 解析字体大小
     */
    private fun parseFontSize(sizeStr: String): Int {
        return when {
            sizeStr.endsWith("px") -> sizeStr.removeSuffix("px").toIntOrNull() ?: 14
            sizeStr.endsWith("dp") -> sizeStr.removeSuffix("dp").toIntOrNull() ?: 14
            sizeStr.endsWith("sp") -> sizeStr.removeSuffix("sp").toIntOrNull() ?: 14
            sizeStr.endsWith("em") -> {
                val em = sizeStr.removeSuffix("em").toFloatOrNull() ?: 1.0f
                (14 * em).toInt()
            }
            else -> sizeStr.toIntOrNull() ?: 14
        }
    }
    
    /**
     * 解析对齐方式
     */
    private fun parseAlignment(alignStr: String): android.text.Layout.Alignment {
        return when (alignStr.lowercase()) {
            "center" -> android.text.Layout.Alignment.ALIGN_CENTER
            "end", "right" -> android.text.Layout.Alignment.ALIGN_OPPOSITE
            else -> android.text.Layout.Alignment.ALIGN_NORMAL
        }
    }

}