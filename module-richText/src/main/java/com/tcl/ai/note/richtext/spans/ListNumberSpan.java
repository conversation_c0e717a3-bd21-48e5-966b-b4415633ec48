package com.tcl.ai.note.richtext.spans;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;
import android.text.Spanned;

import com.tcl.ai.note.richtext.utils.DisplayUtils;


public class ListNumberSpan implements AreListSpan {
    private static final String TAG = "ListNumberSpan";
    private int mNumber;

    public ListNumberSpan(int number) {
        mNumber = number;
    }

    public void setNumber(int number) {
        this.mNumber = number;
    }

    public int getNumber() {
        return this.mNumber;
    }

    public int getLeadingMargin(boolean first) {
        // 根据数字位数动态计算所需边距
        if (mNumber >= 1000) {
            // 四位数及以上：需要更多空间
            return DisplayUtils.dp2px(44);
        } else if (mNumber >= 100) {
            // 三位数：需要额外空间
            return DisplayUtils.dp2px(36);
        } else {
            // 一位数和两位数：使用原有空间
            return DisplayUtils.dp2px(28);
        }
    }


    @Override
    public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top,
                                  int baseline, int bottom, CharSequence text, int start, int end,
                                  boolean first, Layout l) {
        if (((Spanned) text).getSpanStart(this) == start) {
            Paint.Style style = p.getStyle();
            p.setStyle(Paint.Style.FILL);

            float oneNumberWidth = p.measureText(String.valueOf(1));
            String numberText = String.valueOf(mNumber);
            float numberTextWidth = p.measureText(numberText);

            float xPosition;
            String drawText;

            if (dir > 0) {
                // 从左到右的文本方向
                if (mNumber >= 1000) {
                    // 四位数及以上：左对齐，留出足够空间
                    xPosition = x;
                } else if (mNumber >= 100) {
                    // 三位数：左对齐，留出足够空间
                    xPosition = x;
                } else if (mNumber > 9) {
                    // 两位数：左对齐
                    xPosition = x;
                } else {
                    // 一位数：右对齐到两位数的位置
                    xPosition = x + oneNumberWidth;
                }
                drawText = mNumber + ".";
            } else {
                // 从右到左的文本方向
                xPosition = x - numberTextWidth - oneNumberWidth;
                drawText = "." + mNumber;
            }

            if (mNumber != -1) {
                c.drawText(drawText, xPosition, baseline, p);
            }

            p.setStyle(style);
        }
    }
}