package com.tcl.ai.note.richtext.utils

import android.graphics.drawable.GradientDrawable
import android.view.View
import androidx.core.graphics.toColorInt
import com.tcl.ai.note.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.richtext.history.OperationType
import com.tcl.ai.note.richtext.history.RichTextUndoRedoManager
import com.tcl.ai.note.richtext.spans.AreLeadingMarginSpan
import com.tcl.ai.note.richtext.views.AREditText
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.IOException
import java.io.ObjectInputStream
import java.io.ObjectOutputStream


object RichTextKTUtils {
    val original = "<li>​你猜呢才能你免费军军</li>"
    //20:47:33.084  D  fromHtml cost time: 7930 ms
    private val repeated = original.repeat(10)
    var richTextHTML = """
<p>补偿费和会很方便奶粉呢<span style="font-size:28px";>能否你猜呢你nncn保持补充包</span> </p>
<br><p>解放军 </p>
<br><p>how to get a很多好多好多回复回复和当红花旦弧度呵护 </p>
<br></todo><ul>
$repeated
<li style="text-align:center;">​​奶粉呢跟你农村女你</li>
<li>​怒放你能否你麻烦你 </li>
</ul><ol>
<li><span style="font-size:28px";>​部分保持不变你办法帮你</span></li>
<li style="text-align:end;">​​君君</li>
<li>​每次女能男女 <b><u><i><span style="text-decoration:line-through;">你猜呢才能叫农村经济</span></i></u></b> </li>
</ol>
""".trimIndent()
    var richTextDemoStyleEntity: RichTextStyleEntity? = null


    /**
     * 对象转字节数组
     */
    @Throws(IOException::class)
    fun objectToBytes(obj: Any?): ByteArray? {
        ByteArrayOutputStream().use { out ->
            ObjectOutputStream(out).use { sOut ->
                sOut.writeObject(obj)
                sOut.flush()
                return out.toByteArray()
            }
        }
    }

    /**
     * 字节数组转对象
     */
    @Throws(IOException::class, ClassNotFoundException::class)
    fun bytesToObject(bytes: ByteArray?): Any? {
        ByteArrayInputStream(bytes).use { `in` -> ObjectInputStream(`in`).use { sIn -> return sIn.readObject() } }
    }



    @JvmStatic
    fun deleteFile(pathname: String) {
        CoroutineScope(Dispatchers.Main).launch {
            val file = File(pathname)
            if (file.exists()) file.delete()
        }
    }


    /**
     * 创建一个OVAL GradientDrawable
     */
    fun getOvalGradientDrawable(color: Int): GradientDrawable {
        val roundRect = GradientDrawable()
        roundRect.shape = GradientDrawable.OVAL
        roundRect.setColor(color)
        roundRect.setStroke(DisplayUtils.dp2px(1), "#EDEDED".toColorInt())
        roundRect.setSize(DisplayUtils.dp2px(20), DisplayUtils.dp2px(20))
        return roundRect
    }
}


fun View.isVisible(isVisible: Boolean) {
    this.visibility = if (isVisible) {
        View.VISIBLE
    } else {
        View.GONE
    }
}

fun recordApplyStyleToUndoRedo(start: Int, end: Int, isChecked: Boolean, editText: AREditText, styleClass: Class<*>) {
    recordApplyStyleToUndoRedo(start, end, isChecked, editText, 0, styleClass)
}

/**
 * 记录样式应用到撤销/重做栈
 * @param start 操作开始位置
 * @param end 操作结束位置
 * @param isChecked 是否选中（对于待办事项）
 * @param editText 编辑器
 * @param indentLevel 缩进级别（对于缩进操作）
 * @param spanClass 样式类
 */
fun recordApplyStyleToUndoRedo(
    start: Int,
    end: Int,
    isChecked: Boolean,
    editText: AREditText,
    indentLevel: Int = 0,
    spanClass: Class<*>? = null
) {
    // 获取撤销重做管理器
    val undoRedoManager = editText.richTextUndoRedoManager

    // 根据spanClass类型确定操作类型
    val operationType = when (spanClass) {
        AreLeadingMarginSpan::class.java -> {
            // 根据缩进级别变化确定是左缩进还是右缩进
            val oldSpans = editText.text?.getSpans(start, end, AreLeadingMarginSpan::class.java)
            val oldLevel = if (oldSpans?.isNotEmpty() == true) oldSpans[0].level else 0

            if (indentLevel > oldLevel) OperationType.INDENT_RIGHT else OperationType.INDENT_LEFT
        }
        // 其他样式类型...
        else -> OperationType.STYLE_APPLY
    }

    // 记录操作
    undoRedoManager.recordOperation(
        start,
        end,
        operationType,
        spanClass,
        indentLevel,
        isChecked
    )
}
/**
 * 记录删除或插入文本操作到撤销/重做栈，确保线程安全
 *
 * @param startPos 操作开始位置
 * @param before 删除前文本长度
 * @param count 插入文本长度
 * @param isDelete 是否为删除操作
 * @param undoRedoManager 撤销重做管理器
 */
fun recordDeleteStyleToUndoRedo(
    startPos: Int, before: Int, count: Int, isDelete: Boolean,
    undoRedoManager: RichTextUndoRedoManager
) {
    // 使用单例互斥锁确保线程安全
    CoroutineScope(Dispatchers.Main + SupervisorJob()).launch {
        // 使用 Mutex 确保同一时间只有一个协程可以执行记录操作
        withContext(Dispatchers.IO) {
            synchronized(undoRedoManager) {
                if (isDelete) {
                    // 删除文本
                    undoRedoManager.recordOperation(
                        startPos,
                        startPos + before,
                        OperationType.TEXT_DELETE,
                        null
                    )
                } else if (count > 0) {
                    // 插入文本
                    undoRedoManager.recordOperation(
                        startPos,
                        startPos + count,
                        OperationType.TEXT_INSERT,
                        null
                    )
                }
            }
        }
    }
}

/**
 * 正则表达式用于匹配所有不可见字符，包括：
 * - 常规空白字符（空格、制表符、换行符等）
 * - 零宽空格 (U+200B)
 * - 零宽不换行空格 (U+FEFF)
 * - 其他不可见Unicode字符
 */
private val INVISIBLE_CHARS_REGEX = Regex("[\\s\\u200B\\u200C\\u200D\\u2060\\uFEFF]+")

/**
 * 移除字符串中的所有不可见字符（包括零宽空格等）
 * @return 移除不可见字符后的字符串
 */
fun String?.removeInvisibleChars(): String {
    if (this == null) return ""
    return INVISIBLE_CHARS_REGEX.replace(this, "")
}

/**
 * 移除字符串中的所有不可见字符，然后执行trim操作
 * @return 处理后的字符串
 */
fun String?.cleanTrim(): String {
    if (this == null) return ""
    return INVISIBLE_CHARS_REGEX.replace(this, " ").trim()
}

/**
 * 检查字符串是否真正为空（包括只包含不可见字符的情况）
 * @return 如果字符串为null、空字符串或只包含不可见字符，则返回true
 */
fun String?.isTrulyBlank(): Boolean {
    if (this == null) return true
    return this.removeInvisibleChars().isEmpty()
}

/**
 * 获取字符串的可见长度（不计算不可见字符）
 * @return 可见字符的长度
 */
fun String?.visibleLength(): Int {
    if (this == null) return 0
    return this.removeInvisibleChars().length
}
