package com.tcl.ai.note.richtext.converter

import com.tcl.ai.note.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.richtext.data.StyleRange

/**
 * 构建器模式
 */
class RichTextBuilder {
    private var version: String = "1.0"
    private val colorList = mutableListOf<StyleRange>()
    private val fontSizeList = mutableListOf<StyleRange>()
    private val alignList = mutableListOf<StyleRange>()
    private val boldList = mutableListOf<StyleRange>()
    private val italicList = mutableListOf<StyleRange>()
    private val underlineList = mutableListOf<StyleRange>()
    private val strikethroughList = mutableListOf<StyleRange>()
    private val backgroundColorList = mutableListOf<StyleRange>()
    private val indentList = mutableListOf<StyleRange>()
    private val listList = mutableListOf<StyleRange>()

    fun setVersion(version: String) = apply {
        this.version = version
    }

    fun addColor(start: Int, end: Int, color: String) = apply {
        colorList.add(StyleRange(start, end, color))
    }

    fun addBackgroundColor(start: Int, end: Int, color: String) = apply {
        backgroundColorList.add(StyleRange(start, end, color))
    }

    fun addFontSize(start: Int, end: Int, size: String) = apply {
        fontSizeList.add(StyleRange(start, end, size))
    }

    fun addAlign(start: Int, end: Int, alignment: String) = apply {
        alignList.add(StyleRange(start, end, alignment))
    }

    fun addBold(start: Int, end: Int, value: String = "") = apply {
        boldList.add(StyleRange(start, end, value))
    }

    fun addItalic(start: Int, end: Int, value: String = "") = apply {
        italicList.add(StyleRange(start, end, value))
    }

    fun addUnderline(start: Int, end: Int, value: String = "") = apply {
        underlineList.add(StyleRange(start, end, value))
    }
    fun addStrikethrough(start: Int, end: Int)= apply {
        strikethroughList.add(StyleRange(start, end))
    }

    fun addListItem(start: Int, end: Int, value: String,checked: Boolean=false,number: Int=1) = apply {
        listList.add(StyleRange(start, end, value,checked, number = number))
    }

    fun addIndent(start: Int, end: Int, value: String) = apply {
        indentList.add(StyleRange(start, end, value))
    }

    fun build(): RichTextStyleEntity {
        return RichTextStyleEntity(
            version = version,
            fontColor = colorList.toList(),
            fontSize = fontSizeList.toList(),
            align = alignList.toList(),
            bold = boldList.toList(),
            italic = italicList.toList(),
            underline = underlineList.toList(),
            strikethrough = strikethroughList.toList(),
            backgroundColor = backgroundColorList.toList(),
            indent = indentList.toList(),
            list = listList.toList()
        )
    }


}

/**
 * 扩展函数，用于版本兼容性检查
 */
fun RichTextStyleEntity.isVersionCompatible(requiredVersion: String): Boolean {
    return try {
        val currentVersion = this.version.toDoubleOrNull() ?: 0.0
        val required = requiredVersion.toDoubleOrNull() ?: 0.0
        currentVersion >= required
    } catch (e: Exception) {
        false
    }
}
