package com.tcl.ai.note.richtext.converter

import android.graphics.Typeface
import android.text.Editable
import android.text.Spanned
import android.text.style.*
import android.widget.EditText
import com.tcl.ai.note.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.richtext.spans.*

/**
 * RichTextStyleEntity 与 EditText Span 的转换器
 */
object RichTextStyleSpanToEntityConverter {
    

    /**
     * 从 EditText 提取 RichTextStyleEntity
     */
    fun extractFromEditText(editText: EditText, version: String = "1.0"): RichTextStyleEntity {
        val text = editText.text
        if (text !is Spanned) {
            return RichTextStyleEntity(version = version)
        }
        
        val builder = RichTextBuilder().setVersion(version)
        
        // 提取颜色样式
        extractColorSpans(text, builder)
        extractBackgroundColorSpans(text, builder)
        // 提取字体大小样式
        extractFontSizeSpans(text, builder)
        
        // 提取对齐样式
        extractAlignmentSpans(text, builder)
        
        // 提取粗体样式
        extractBoldSpans(text, builder)
        
        // 提取斜体样式
        extractItalicSpans(text, builder)
        
        // 提取下划线样式
        extractUnderlineSpans(text, builder)

        // 提取删除线样式
        extractStrikethroughSpans(text, builder)
        // 提取背景色样式
        extractBackgroundColorSpans(text,builder)
        extractIndentSpans(text, builder)
        // 提取列表项样式
        extractListSpans(text, builder)
        
        return builder.build()
    }

    private fun extractIndentSpans(text: Editable, builder: RichTextBuilder) {
        text.getSpans(0, text.length, AreLeadingMarginSpan::class.java).forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            val indent = span.level.toString()
            builder.addIndent(start, end, indent)
        }
    }

    private fun extractBackgroundColorSpans(text: Editable, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, AreBackgroundColorSpan::class.java)
        spans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            val color = String.format("#%08X", span.backgroundColor)
            builder.addBackgroundColor(start, end, color)
        }
    }

    private fun extractStrikethroughSpans(text: Editable, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, StrikethroughSpan::class.java)
        spans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            builder.addStrikethrough(start, end)
        }
    }

    /**
     * 提取颜色样式
     */
    private fun extractColorSpans(text: Spanned, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, AreForegroundColorSpan::class.java)
        spans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            val color = String.format("#%08X", span.foregroundColor)
            builder.addColor(start, end, color)
        }
    }
    
    /**
     * 提取字体大小样式
     */
    private fun extractFontSizeSpans(text: Spanned, builder: RichTextBuilder) {
        // AbsoluteSizeSpan
        val absoluteSpans = text.getSpans(0, text.length, AbsoluteSizeSpan::class.java)
        absoluteSpans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            val size = if (span.dip) "${span.size}dp" else "${span.size}px"
            builder.addFontSize(start, end, size)
        }
        
        // RelativeSizeSpan
        val relativeSpans = text.getSpans(0, text.length, RelativeSizeSpan::class.java)
        relativeSpans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            val size = "${span.sizeChange}em"
            builder.addFontSize(start, end, size)
        }
    }
    
    /**
     * 提取对齐样式
     */
    private fun extractAlignmentSpans(text: Spanned, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, AlignmentSpan::class.java)
        spans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            val alignment = when (span.alignment) {
                android.text.Layout.Alignment.ALIGN_CENTER -> "center"
                android.text.Layout.Alignment.ALIGN_OPPOSITE -> "end"
                else -> "start"
            }
            builder.addAlign(start, end, alignment)
        }
    }
    
    /**
     * 提取粗体样式
     */
    private fun extractBoldSpans(text: Spanned, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, StyleSpan::class.java)
        spans.forEach { span ->
            if (span.style == Typeface.BOLD || span.style == Typeface.BOLD_ITALIC) {
                val start = text.getSpanStart(span)
                val end = text.getSpanEnd(span)
                builder.addBold(start, end)
            }
        }
    }
    
    /**
     * 提取斜体样式
     */
    private fun extractItalicSpans(text: Spanned, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, StyleSpan::class.java)
        spans.forEach { span ->
            if (span.style == Typeface.ITALIC || span.style == Typeface.BOLD_ITALIC) {
                val start = text.getSpanStart(span)
                val end = text.getSpanEnd(span)
                builder.addItalic(start, end)
            }
        }
    }
    
    /**
     * 提取下划线样式
     */
    private fun extractUnderlineSpans(text: Spanned, builder: RichTextBuilder) {
        val spans = text.getSpans(0, text.length, AreUnderlineSpan::class.java)
        spans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            builder.addUnderline(start, end)
        }
    }
    
    /**
     * 提取列表项样式
     */
    private fun extractListSpans(text: Spanned, builder: RichTextBuilder) {
        val allListRanges = mutableListOf<ListStyleRange>()

        // 1. 提取各类列表符，并收集到allListRanges
        val todoSpans = text.getSpans(0, text.length, UpcomingListSpan::class.java)
        todoSpans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            allListRanges.add(ListStyleRange(start, end, "todo", checked = span.isChecked))
        }
        val numberSpans = text.getSpans(0, text.length, ListNumberSpan::class.java)
        numberSpans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            allListRanges.add(ListStyleRange(start, end, "number", number = span.number))
        }
        val bulletSpans = text.getSpans(0, text.length, ListBulletSpan::class.java)
        bulletSpans.forEach { span ->
            val start = text.getSpanStart(span)
            val end = text.getSpanEnd(span)
            allListRanges.add(ListStyleRange(start, end, "bullet"))
        }

        // 2. 区间去重&去包含，最后保留的为最终列表段
        val mergedRanges = mergeNonOverlappingListRanges(allListRanges)

        // 3. 插入到 RichTextBuilder
        for (r in mergedRanges) {
            builder.addListItem(r.start, r.end, r.type, r.checked, r.number)
        }
    }

    data class ListStyleRange(
        val start: Int,
        val end: Int,
        val type: String,
        val checked: Boolean = false,
        val number: Int = 1
    )

    /**
     * 合并规则：
     * - 同区间（start,end）只保留List里最后一个（比如有多个，无论类型，保留最新）
     * - 排除区间嵌套（完全包含），只保留不被覆盖的最外层（或末次出现）
     * - 排序后输出
     */
    private fun mergeNonOverlappingListRanges(list: List<ListStyleRange>): List<ListStyleRange> {
        // 1. 同区间只留最后一个
        val unique = list.groupBy { Pair(it.start, it.end) }
            .map { it.value.last() }
            .sortedWith(compareBy({ it.start }, { it.end }))
            .toMutableList()

        // 2. 排除完全被包含的区间
        //   - 若一个区间完全被另一个区间包围（start >= other.start && end <= other.end），保留大的，去掉小的
        val filtered = mutableListOf<ListStyleRange>()
        for (i in unique.indices) {
            val cur = unique[i]
            // 是否有其它区间完全包含它
            var contained = false
            for (j in unique.indices) {
                if (i == j) continue
                val other = unique[j]
                // 完全包裹(区间不等长)
                if (other.start <= cur.start && other.end >= cur.end && (other.start != cur.start || other.end != cur.end)) {
                    contained = true
                    break
                }
            }
            if (!contained) {
                filtered.add(cur)
            }
        }
        return filtered
    }

}