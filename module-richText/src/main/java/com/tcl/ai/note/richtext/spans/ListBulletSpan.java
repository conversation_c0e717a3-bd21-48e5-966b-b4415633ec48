package com.tcl.ai.note.richtext.spans;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.text.Layout;
import android.text.Spanned;

import androidx.annotation.ColorInt;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.Px;

import com.tcl.ai.note.richtext.utils.DisplayUtils;


public class ListBulletSpan implements AreListSpan {
    private static final String TAG = "ListBulletSpan";
    // Bullet is slightly bigger to avoid aliasing artifacts on mdpi devices.
    private static final int STANDARD_BULLET_RADIUS = 5;
    public static final int STANDARD_GAP_WIDTH = 2;
    private static final int STANDARD_COLOR = 0;

    @Px
    private final int mGapWidth;
    @Px
    private final int mBulletRadius;
    private Path mBulletPath = null;
    @ColorInt
    private final int mColor;
    private final boolean mWantColor;

    public ListBulletSpan() {
        this(STANDARD_GAP_WIDTH, STANDARD_COLOR, false, STANDARD_BULLET_RADIUS);
    }

    private ListBulletSpan(int gapWidth, @ColorInt int color, boolean wantColor,
                           @IntRange(from = 0) int bulletRadius) {
        mGapWidth = gapWidth;
        mBulletRadius = bulletRadius;
        mColor = color;
        mWantColor = wantColor;
    }

    private static final int SYMBOL_MARGIN = DisplayUtils.dp2px(13);

    public int getLeadingMargin(boolean first) {
        return DisplayUtils.dp2px(28);
    }

//	@Override
//	public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top,
//                                  int baseline, int bottom, CharSequence text, int start, int end,
//                                  boolean first, Layout l) {
//
//		if (((Spanned) text).getSpanStart(this) == start) {
//			Paint.Style style = p.getStyle();
//			p.setStyle(Paint.Style.FILL);
//
//			c.drawText("\u2022", x + dir + SYMBOL_MARGIN, baseline, p);
//
//			p.setStyle(style);
//		}
//	}

    @Override
    public void drawLeadingMargin(@NonNull Canvas canvas, @NonNull Paint paint, int x, int dir,
                                  int top, int baseline, int bottom,
                                  @NonNull CharSequence text, int start, int end,
                                  boolean first, @Nullable Layout layout) {
        if (((Spanned) text).getSpanStart(this) == start) {
            Paint.Style style = paint.getStyle();
            int oldcolor = 0;

            if (mWantColor) {
                oldcolor = paint.getColor();
                paint.setColor(mColor);
            }

            paint.setStyle(Paint.Style.FILL);

//			if (layout != null) {
//				// "bottom" position might include extra space as a result of line spacing
//				// configuration. Subtract extra space in order to show bullet in the vertical
//				// center of characters.
//				final int line = layout.getLineForOffset(start);
//				bottom = bottom - layout.getLineExtra(line);
//			}
            Paint.FontMetricsInt fontMetrics = paint.getFontMetricsInt();

            final float yPosition = ((top + baseline) / 2f) + fontMetrics.descent;
            final float xPosition = (float) x + dir * SYMBOL_MARGIN;

            if (canvas.isHardwareAccelerated()) {
                if (mBulletPath == null) {
                    mBulletPath = new Path();
                    mBulletPath.addCircle(0.0f, 0.0f, mBulletRadius, Path.Direction.CW);
                }

                canvas.save();
                canvas.translate(xPosition, yPosition);
                canvas.drawPath(mBulletPath, paint);
                canvas.restore();
            } else {
                canvas.drawCircle(xPosition, yPosition, mBulletRadius, paint);
            }

            if (mWantColor) {
                paint.setColor(oldcolor);
            }

            paint.setStyle(style);
        }
    }

}
